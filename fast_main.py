#!/usr/bin/env python3
"""
Fast Trading System Startup - IMMEDIATE EXECUTION
CRITICAL: This executes real money trades on Bybit with minimal initialization time
"""

import os
import sys
import asyncio
import time
from pathlib import Path
from datetime import datetime

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Force Bybit-only environment
os.environ["BYBIT_ONLY_MODE"] = "true"
os.environ["COINBASE_ENABLED"] = "false"
os.environ["LIVE_TRADING"] = "true"
os.environ["REAL_MONEY_TRADING"] = "true"

async def fast_main():
    """Fast main function with minimal initialization"""
    print("🚀 [FAST-MAIN] Starting fast trading system...")
    
    try:
        # Import only essential components
        from src.exchanges.bybit_client_fixed import BybitClientFixed
        from src.trading.multi_currency_trading_engine import MultiCurrencyTradingEngine
        
        # Get API credentials
        bybit_api_key = os.getenv('BYBIT_API_KEY')
        bybit_api_secret = os.getenv('BYBIT_API_SECRET')
        
        if not bybit_api_key or not bybit_api_secret:
            raise RuntimeError("CRITICAL: Missing Bybit API credentials")
        
        print("🔧 [FAST-MAIN] Initializing Bybit client...")
        bybit_client = BybitClientFixed(
            api_key=bybit_api_key,
            api_secret=bybit_api_secret,
            testnet=False  # REAL MONEY TRADING ONLY
        )
        
        if not bybit_client.session:
            raise RuntimeError("CRITICAL: Failed to initialize Bybit client")
        
        print("✅ [FAST-MAIN] Bybit client initialized successfully")
        
        # Create exchange clients dict
        exchange_clients = {
            'bybit': bybit_client
        }
        
        print("🔧 [FAST-MAIN] Initializing trading engine...")
        trading_engine = MultiCurrencyTradingEngine(exchange_clients)
        
        print("✅ [FAST-MAIN] Trading engine initialized")
        print("🎯 [FAST-MAIN] Starting continuous trading loop...")
        
        # Start continuous trading
        loop_count = 0
        while True:
            try:
                loop_count += 1
                print(f"\n🔄 [TRADING-LOOP] Cycle {loop_count} - {datetime.now().strftime('%H:%M:%S')}")
                
                # Execute trading cycle
                await trading_engine.execute_comprehensive_trading_cycle()
                
                # Short delay between cycles
                await asyncio.sleep(30)  # 30 seconds between cycles
                
            except KeyboardInterrupt:
                print("\n🛑 [FAST-MAIN] Trading stopped by user")
                break
            except Exception as e:
                print(f"❌ [TRADING-LOOP] Error in cycle {loop_count}: {e}")
                await asyncio.sleep(60)  # Wait 1 minute on error
                
    except Exception as e:
        print(f"❌ [FAST-MAIN] Critical error: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    print("⚡ [FAST-STARTUP] Fast trading system starting...")
    print("🎯 [FAST-STARTUP] Minimal initialization for immediate trading")
    print("💰 [FAST-STARTUP] REAL MONEY TRADING - Bybit only")
    
    try:
        exit_code = asyncio.run(fast_main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 [FAST-STARTUP] System stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"❌ [FAST-STARTUP] System failed: {e}")
        sys.exit(1)
